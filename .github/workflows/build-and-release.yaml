name: Build and Attach Package

on:
  release:
    types: [created]

jobs:
  build-and-upload:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Install Poetry
        run: |
          pip install poetry

      - name: Build Package
        run: |
          poetry build
          pwd
          ls dist/
      
      - name: Upload Release Asset
        uses: softprops/action-gh-release@v1
        with:
          files: dist/*.whl
        env:
          GITHUB_TOKEN: ${{ secrets.MY_PERSONAL_ACCESS_TOKEN }}


