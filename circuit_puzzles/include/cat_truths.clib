(
  (defun-inline cat_truth_data_to_truth_struct (innerpuzhash cat_struct my_id this_coin_info)
    (c
      (c
        innerpuzhash
        cat_struct
      )
      (c
        my_id
        this_coin_info
      )
    )
  )

  ; CAT Truths is: ((Inner puzzle hash . (MOD hash . (MOD hash hash . TAIL hash))) . (my_id . (my_parent_info my_puzhash my_amount)))

  (defun-inline my_inner_puzzle_hash_cat_truth (Truths) (f (f Truths)))
  (defun-inline cat_struct_truth (Truths) (r (f Truths)))
  (defun-inline my_id_cat_truth (Truths) (f (r Truths)))
  (defun-inline my_coin_info_truth (Truths) (r (r Truths)))
  (defun-inline my_amount_cat_truth (Truths) (f (r (r (my_coin_info_truth Truths)))))
  (defun-inline my_full_puzzle_hash_cat_truth (Truths) (f (r (my_coin_info_truth Truths))))
  (defun-inline my_parent_cat_truth (Truths) (f (my_coin_info_truth Truths)))


  ; CAT mod_struct is: (MOD_HASH MOD_HASH_hash TAIL_PROGRAM TAIL_PROGRAM_hash)

  (defun-inline cat_mod_hash_truth (Truths) (f (cat_struct_truth Truths)))
  (defun-inline cat_mod_hash_hash_truth (Truths) (f (r (cat_struct_truth Truths))))
  (defun-inline cat_tail_program_hash_truth (Truths) (f (r (r (cat_struct_truth Truths)))))
)
