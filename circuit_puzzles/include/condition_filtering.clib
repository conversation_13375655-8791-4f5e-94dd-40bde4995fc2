(

  (defun and_ (CLAUSES)
    (if (r CLAUSES)
      (qq (if (unquote (f CLAUSES)) (unquote (and_ (r CLAUSES))) ()))
      (f CLAUSES)
    )
  )

  ; logical AND function that evaluates clauses left to right
  (defmac and CLAUSES (if CLAUSES (and_ CLAUSES) ONE))

  ; true/false for non-protocol/protocol CREATE_..._ANNOUNCEMENT condition
  (defun-inline is-valid-ann-cond (condition_body)
    (not
      (and
        (l condition_body)
        (not (l (f condition_body)))
        (= (strlen (f condition_body)) 33)
        (= (substr (f condition_body) 0 ONE) PROTOCOL_PREFIX)
      )
    )
  )

  ; true/false for non-protocol/protocol ..._MESSAGE condition
  (defun-inline is-valid-msg-cond (condition_body)
    (not
      (and
        (l condition_body)
        (l (r condition_body))
        (not (l (f (r condition_body))))
        (= (strlen (f (r condition_body))) 33)
        (= (substr (f (r condition_body)) 0 ONE) PROTOCOL_PREFIX)
      )
    )
  )

  ; true/false if non-protocol/protocol remark condition
  (defun-inline is-valid-rmk-cond (condition_body)
    (not
      (and
        (l condition_body)
        (not (l (f condition_body)))
        (= (strlen (f condition_body)) ONE)
        (= (substr (f condition_body) 0 ONE) PROTOCOL_PREFIX)
      )
    )
  )


  ; fails if protocol condition encountered, o/w returns 1
  (defun fail-on-protocol-condition ((@ conditions ((condition_code . condition_body) . rest_of_conditions)))
    (if conditions
      (if (= condition_code REMARK)
        (assert
          (is-valid-rmk-cond condition_body)
          (fail-on-protocol-condition rest_of_conditions)
        )
        (if (any (= condition_code SEND_MESSAGE) (= condition_code RECEIVE_MESSAGE))
          (assert
            (is-valid-msg-cond condition_body)
            (fail-on-protocol-condition rest_of_conditions)
          )
          (if (any (= condition_code CREATE_COIN_ANNOUNCEMENT) (= condition_code CREATE_PUZZLE_ANNOUNCEMENT))
            (assert
              (is-valid-ann-cond condition_body)
              (fail-on-protocol-condition rest_of_conditions)
            )
            ; all other conditions allowed
            (fail-on-protocol-condition rest_of_conditions)
          )
        )
      )
      ONE
    )
  )

  ; fails if create coin or protocol condition encountered, o/w returns 1
  (defun fail-on-invalid-custom-statutes-conditions ((@ conditions ((condition_code . condition_body) . rest_of_conditions)))
    (if conditions
      (if (any (= condition_code CREATE_COIN) (= condition_code REMARK))
        (x) ; no create coin allowed
        (if (any (= condition_code SEND_MESSAGE) (= condition_code RECEIVE_MESSAGE))
          (assert
            (is-valid-msg-cond condition_body)
            (fail-on-protocol-condition-or-create-coin rest_of_conditions)
          )
          (if (any (= condition_code CREATE_COIN_ANNOUNCEMENT) (= condition_code CREATE_PUZZLE_ANNOUNCEMENT))
            (assert
              (is-valid-ann-cond condition_body)
              (fail-on-protocol-condition-or-create-coin rest_of_conditions)
            )
            ; other conditions allowed
            (fail-on-protocol-condition-or-create-coin rest_of_conditions)
          )
        )
      )
      ONE
    )
  )

  ; fails if create coin or protocol condition encountered, o/w returns 1
  (defun fail-on-protocol-condition-or-create-coin ((@ conditions ((condition_code . condition_body) . rest_of_conditions)))
    (if conditions
      (if (= condition_code CREATE_COIN)
        (x) ; no create coin allowed
        (if (= condition_code REMARK)
          (assert
            (is-valid-rmk-cond condition_body)
            (fail-on-protocol-condition-or-create-coin rest_of_conditions)
          )
          (if (any (= condition_code SEND_MESSAGE) (= condition_code RECEIVE_MESSAGE))
            (assert
              (is-valid-msg-cond condition_body)
              (fail-on-protocol-condition-or-create-coin rest_of_conditions)
            )
            (if (any (= condition_code CREATE_COIN_ANNOUNCEMENT) (= condition_code CREATE_PUZZLE_ANNOUNCEMENT))
              (assert
                (is-valid-ann-cond condition_body)
                (fail-on-protocol-condition-or-create-coin rest_of_conditions)
              )
              ; other conditions allowed
              (fail-on-protocol-condition-or-create-coin rest_of_conditions)
            )
          )
        )
      )
      ONE
    )
  )


  ; fails if protocol condition encountered
  ; fails if not exactly one create coin encountered
  ; returns body of create coin condition and all other conditions
  (defun filter-and-extract-unique-create-coin (
        (@ conditions ((condition_code . condition_body) . rest_of_conditions))
        found_create_coin
        filtered_conditions
      )
    (if conditions
      (if (= condition_code CREATE_COIN)
        (if found_create_coin
          (x) ; only one create coin condition allowed
          ; this is the first create coin encountered. extract condition body
          (filter-and-extract-unique-create-coin
            rest_of_conditions
            condition_body ; extract create coin condition body
            filtered_conditions ; don't prepend to conditions list
          )
        )
        (if (= condition_code REMARK)
          (assert
            (is-valid-rmk-cond condition_body)
            (filter-and-extract-unique-create-coin rest_of_conditions found_create_coin (c (f conditions) filtered_conditions))
          )
          (if (any (= condition_code SEND_MESSAGE) (= condition_code RECEIVE_MESSAGE))
            (assert
              (is-valid-msg-cond condition_body)
              (filter-and-extract-unique-create-coin rest_of_conditions found_create_coin (c (f conditions) filtered_conditions))
            )
            (if (any (= condition_code CREATE_COIN_ANNOUNCEMENT) (= condition_code CREATE_PUZZLE_ANNOUNCEMENT))
              (assert
                (is-valid-ann-cond condition_body)
                (filter-and-extract-unique-create-coin rest_of_conditions found_create_coin (c (f conditions) filtered_conditions))
              )
              ; all other conditions allowed
              (filter-and-extract-unique-create-coin rest_of_conditions found_create_coin (c (f conditions) filtered_conditions))
            )
          )
        )
      )
      (assert
        found_create_coin ; must have found a create coin
        (list found_create_coin filtered_conditions)
      )
    )
  )

)
