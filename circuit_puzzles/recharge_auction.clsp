(mod (OPERATIONS BYC_TAIL_MOD_HASH
      MOD_HASH STATUTES_STRUCT LAUNCHER_ID AUCTION_PARAMS LAST_BID
      lineage_proof operation input_conditions . op_args)

  (include *standard-cl-23.1*)
  (include prefixes.clib)
  (include utils.clib)
  (include condition_codes.clib)
  (include condition_filtering.clib)
  (include curry.clib)
  (include statutes_utils.clib)

  (assign-inline
    byc_tail_hash (curry_hashes BYC_TAIL_MOD_HASH (sha256tree STATUTES_STRUCT))
    (assert
      (contains OPERATIONS (sha256tree operation))
      (fail-on-protocol-condition-or-create-coin input_conditions)
      (a
        operation
        (list
          (list MOD_HASH STATUTES_STRUCT byc_tail_hash LAUNCHER_ID AUCTION_PARAMS LAST_BID)
          lineage_proof input_conditions op_args
        )
      )
    )
  )
)