(mod (
      MUTATION_PROGRAM_HASH MOD_HASH
      STATUTES_STRUCT
      PRICE_INFOS
      ; solution
      oracle_program_args
      input_announcement_asserts
      . announce_args
     )

  (include *standard-cl-23.1*)
  (include curry.clib)
  (include condition_codes.clib)
  (include statutes_utils.clib)
  (include utils.clib)
  (include oracle.clib)


  (if (and
        oracle_program_args
        (= (sha256tree (f oracle_program_args)) MUTATION_PROGRAM_HASH)
      )
    (a
      (f oracle_program_args)
      (list
        MOD_HASH
        STATUTES_STRUCT
        PRICE_INFOS
        (r oracle_program_args)
        input_announcement_asserts
      )
    )
    ; no updates, just assert and announce
    ; recreate yourself to conserve updates so far
    (verify-announcement-asserts
      input_announcement_asserts
      (assign
        (current_timestamp price_delay) announce_args
        cutoff (if (all current_timestamp price_delay) (- current_timestamp price_delay) 0)
        (last_matured_price_info . _) (cut-price-infos PRICE_INFOS cutoff ())
        (oldest_price . oldest_updated) (if last_matured_price_info last_matured_price_info (c () ()))
        (list
          (list CREATE_COIN
            (curry_hashes MOD_HASH
              (sha256 ONE MOD_HASH)
              (sha256tree STATUTES_STRUCT)
              (sha256tree PRICE_INFOS)
            )
            ; min singleton amount, force it to be this
            ONE
          )
          (if oldest_updated
            (list CREATE_PUZZLE_ANNOUNCEMENT (sha256tree (c oldest_price (c oldest_updated (c current_timestamp price_delay)))))
            (x)  ; no matured prices available, fail. this occurs on launch and potentially when price_delay exceeds STATUTE_PRICE_DELAY
          )
          (list REMARK oldest_price oldest_updated current_timestamp price_delay)
          (list ASSERT_BEFORE_SECONDS_ABSOLUTE (+ current_timestamp MAX_TX_BLOCK_TIME ))
          ; current_timestamp - 1 min should've passed already too, this is to ensure current timestamp is
          ; within a boundary of last_block > current_timestamp - 1 block < next block
          (list ASSERT_SECONDS_ABSOLUTE (- current_timestamp MAX_TX_BLOCK_TIME))
        )
      )
    )
  )
)
