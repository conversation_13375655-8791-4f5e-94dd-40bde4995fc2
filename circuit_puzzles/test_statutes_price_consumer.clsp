(mod (STATUTES_STRUCT statutes_inner_puzzle_hash price_info)

  (include *standard-cl-23.1*)
  (include condition_codes.clib)
  (include curry.clib)
  (include statutes_utils.clib)
  (include utils.clib)

  (list
    (assert-price-info
      (curry_hashes (f STATUTES_STRUCT)
        (sha256tree STATUTES_STRUCT)
        statutes_inner_puzzle_hash
      )
      price_info
    )
    (list REMARK price_info)
    (list REMARK "puzzle hash for statutes: "
      (curry_hashes (f STATUTES_STRUCT)
        (sha256tree STATUTES_STRUCT)
        statutes_inner_puzzle_hash
      )
    )
  )
)