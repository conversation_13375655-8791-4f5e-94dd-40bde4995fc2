(mod (
    CAT_MOD_HASH OFFER_MOD_HASH
    (MOD_HASH STATUTES_STRUCT BYC_TAIL_HASH LAUNCHER_ID AUCTION_PARAMS LAST_BID)
    lineage_proof input_conditions
    (
      (byc_bid_amount . crt_bid_amount) ; new bid
      target_puzzle_hash
      current_timestamp
      my_coin_id
    )
  )

  (include *standard-cl-23.1*)
  (include prefixes.clib)
  (include curry.clib)
  (include condition_codes.clib)
  (include utils.clib)

  (assign
    (
      start_time
      auction_ttl
      bid_ttl
      min_crt_price
      min_byc_bid_amount
      min_bid_increase_bps
    ) AUCTION_PARAMS
    ((last_byc_bid_amount . last_crt_bid_amount) last_target_puzzle_hash last_bid_timestamp) (
      if LAST_BID
        LAST_BID
        (list (c 0 0) () 0)
    )
    crt_price (/ (* byc_bid_amount PRECISION) crt_bid_amount)
    last_crt_price (if last_byc_bid_amount (/ (* last_byc_bid_amount PRECISION) last_crt_bid_amount) 0)
    (assert
      LAUNCHER_ID ; can't bid if auction hasn't started yet
      (> crt_price min_crt_price)
      (any
        (= last_crt_price 0)
        (> crt_price (/ (* last_crt_price (+ PRECISION_BPS min_bid_increase_bps)) PRECISION_BPS))
      )
      ; bids can't be smaller than this
      (> byc_bid_amount min_byc_bid_amount)
      ; auction hasn't timed out yet
      (> auction_ttl (- current_timestamp start_time))
      ; bid hasn't timed out yet
      (any (= last_bid_timestamp 0) (> bid_ttl (- current_timestamp last_bid_timestamp)))
      (li
        ; update LAST_BID
        (list
          CREATE_COIN
          (curry_hashes
            MOD_HASH
            (sha256 ONE MOD_HASH)
            (sha256tree STATUTES_STRUCT)
            (sha256 ONE LAUNCHER_ID)
            (sha256tree AUCTION_PARAMS)
            (sha256tree (list (c byc_bid_amount crt_bid_amount) target_puzzle_hash current_timestamp))
          )
          byc_bid_amount
        )
        (list REMARK
          PROTOCOL_PREFIX
          LAUNCHER_ID
          AUCTION_PARAMS
          (list (c byc_bid_amount crt_bid_amount) target_puzzle_hash current_timestamp)
        )
        (list ASSERT_MY_AMOUNT last_byc_bid_amount)
        ; current_time minus one tx block time should already be in the past
        (list ASSERT_SECONDS_ABSOLUTE (- current_timestamp MAX_TX_BLOCK_TIME))
        ; make sure that current_timestamp hasn't happen yet, allow it to be in mempool for 5 tx blocks
        (list ASSERT_BEFORE_SECONDS_ABSOLUTE (+ current_timestamp (* 5 MAX_TX_BLOCK_TIME)))
        (list ASSERT_MY_PUZZLE_HASH
          (curry_hashes CAT_MOD_HASH
            (sha256 ONE CAT_MOD_HASH)
            (sha256 ONE BYC_TAIL_HASH)
            (curry_hashes
              MOD_HASH
              (sha256 ONE MOD_HASH)
              (sha256tree STATUTES_STRUCT)
              (sha256 ONE LAUNCHER_ID)
              (sha256tree AUCTION_PARAMS)
              (sha256tree LAST_BID)
            )
          )
        )
        (list ASSERT_MY_PARENT_ID
          (calculate-coin-id
            (f lineage_proof)
            (curry_hashes
              CAT_MOD_HASH
              (sha256 ONE CAT_MOD_HASH)
              (sha256 ONE BYC_TAIL_HASH)
              (tree_hash_of_apply MOD_HASH (f (r (r lineage_proof))))
            )
            ; parent bid amount
            (f (r lineage_proof))
          )
        )
        &rest
        (if last_target_puzzle_hash
          (c
            (list ASSERT_MY_COIN_ID my_coin_id)
            (c
              (list ASSERT_PUZZLE_ANNOUNCEMENT
                (sha256
                  (curry_hashes CAT_MOD_HASH
                    (sha256 ONE CAT_MOD_HASH)
                    (sha256 ONE BYC_TAIL_HASH)
                    OFFER_MOD_HASH
                  )
                  (sha256tree
                    (c my_coin_id
                    (list
                      (list last_target_puzzle_hash last_byc_bid_amount (list last_target_puzzle_hash)))))
                )
              )
              input_conditions
            )
          )
          input_conditions
        )
      )
    )
  )
)