(mod
  (
    CAT_MOD_HASH BYC_TAIL_MOD_HASH RUN_TAIL_MOD_HASH TREASURY_MOD_HASH OFFER_MOD_HASH
    (@ VAULT_STATE
      (
       COLLATERAL PRINCIPAL AUCTION_STATE INNER_PUZZLE_HASH
       STATUTES_STRUCT DISCOUNTED_PRINCIPAL statutes_puzzle_hash
      )
    )
    (@ args
      (
        current_timestamp
        byc_bid_amount
        byc_melting_coin_info
        byc_treasury_coin_info
        min_treasury_delta
        target_puzzle_hash
        my_coin_id
      )
    )
  )

  (include *standard-cl-23.1*)
  (include utils.clib)
  (include vault.clib)
  (include statutes_utils.clib)
  (include condition_codes.clib)
  (include curry.clib)

  (assign
    statutes_struct_hash (sha256tree STATUTES_STRUCT)
    byc_tail_hash (curry_hashes BYC_TAIL_MOD_HASH statutes_struct_hash)
    ; parse auction state as we'll need to verify it's still running and hasn't timed out
    (
      auction_start_time
      start_price
      step_price_decrease_factor
      step_time_interval
      initiator_puzzle_hash
      initiator_incentive_balance
      auction_ttl
      byc_to_treasury_balance
      byc_to_melt_balance
      minimum_bid_amount
    ) AUCTION_STATE
    ; first pay initiator
    initiator_incentive (if (> byc_bid_amount initiator_incentive_balance)
      initiator_incentive_balance
      byc_bid_amount
    )
    remaining_byc_bid_amount (- byc_bid_amount initiator_incentive)
    ; then pay treasury
    byc_to_treasury (if (> remaining_byc_bid_amount byc_to_treasury_balance)
      byc_to_treasury_balance
      remaining_byc_bid_amount
    )
    ; then melt
    byc_to_melt (- remaining_byc_bid_amount byc_to_treasury)
    ; leftover debt
    debt (+ initiator_incentive_balance byc_to_treasury_balance byc_to_melt_balance)
    leftover_debt (- debt byc_bid_amount)
    ; use all byc provided by keeper to get a piece of collateral
    bid_xch_collateral_amount_pre (/
      (/
        (* byc_bid_amount PRECISION_BPS PRICE_PRECISION MOJOS)
        (-
          (* start_price PRECISION_BPS)
          (*
            (*
              start_price
              step_price_decrease_factor
            ) ; price decrease per step
            (/
              (- current_timestamp auction_start_time)
              step_time_interval
            ) ; no. (completed) steps
          ) ; total price decrease
        ) ; auction price
      )
    1000
    ) ; amount of collateral (in mojos)
    bid_xch_collateral_amount (
      if (> bid_xch_collateral_amount_pre COLLATERAL)
        ; if the amount of collateral is more than the vault has, use all of it, don't raise error
        COLLATERAL
        bid_xch_collateral_amount_pre
    )
    leftover_collateral (- COLLATERAL bid_xch_collateral_amount)
    byc_coin_id_to_melt (if byc_melting_coin_info
      (calculate-byc-coin-id
        CAT_MOD_HASH
        byc_tail_hash
        (list
          (f byc_melting_coin_info)
          (r byc_melting_coin_info)
          RUN_TAIL_MOD_HASH
        )
      )
      ()
    )
    (treasury_parent treasury_launcher_id treasury_ring_prev_launcher_id treasury_amount) (if byc_treasury_coin_info byc_treasury_coin_info (list 0 0 0 0))
    treasury_coin_id (if byc_treasury_coin_info
      (calculate-byc-coin-id
        CAT_MOD_HASH
        byc_tail_hash
        (list
          treasury_parent
          treasury_amount
          (curry_hashes
            TREASURY_MOD_HASH
            (sha256 ONE TREASURY_MOD_HASH)
            statutes_struct_hash
            (sha256 ONE treasury_launcher_id)
            (sha256 ONE treasury_ring_prev_launcher_id)
          )
        )
      )
      ()
    )
    new_treasury_amount (+ treasury_amount byc_to_treasury)
    auction_state (
      ; update auction state based on the bid, which mostly means to set it to null if all debt was paid off
      ; which will push the vault back into usable state for owner of inner puzzle
      if (= leftover_debt 0)
        ()
        (list
          auction_start_time
          start_price
          step_price_decrease_factor
          step_time_interval
          initiator_puzzle_hash
          (- initiator_incentive_balance initiator_incentive)
          auction_ttl
          (- byc_to_treasury_balance byc_to_treasury) ; byc to treasury balance
          (- byc_to_melt_balance byc_to_melt)  ; byc to melt balance
          minimum_bid_amount
        )
    )
    (assert
      (> leftover_debt -1)
      (> byc_bid_amount 0)
      (any  ; either bid amount is higher than min amount or is equal to the debt or there is no collateral is left
        (> byc_bid_amount minimum_bid_amount)
        (all
          (> minimum_bid_amount (- debt ONE))
          (= byc_bid_amount debt)
        )
        (= leftover_collateral 0)
      )
      (> current_timestamp auction_start_time)
      ; check that we haven't passed auction TTL
      ; is previous auction still running by checking if auction_start_time hasn't timed out
      (> auction_ttl (- current_timestamp auction_start_time))
      ; sanity check for time
      (> current_timestamp (- auction_start_time 1))
      ; check that there is collateral to bid for
      (> COLLATERAL 0)
      (> leftover_collateral -1)
      (any
        (> byc_to_treasury min_treasury_delta)
        (= byc_to_treasury byc_to_treasury_balance)
        (= byc_to_treasury 0)
        (= leftover_collateral 0)
      )
      (list 
        (list
          leftover_collateral
          0 ; leftover_principal
          auction_state
          INNER_PUZZLE_HASH
          0 ; discounted_principal
        )
        (assign
          conditions (list
            (list ASSERT_SECONDS_ABSOLUTE (- current_timestamp MAX_TX_BLOCK_TIME))
            (list ASSERT_BEFORE_SECONDS_ABSOLUTE (+ current_timestamp MAX_TX_BLOCK_TIME))
            ; validate coin id
            (list ASSERT_MY_COIN_ID my_coin_id)
            ; signal to tail that it can melt the BYC
            (if (> byc_to_melt 0)
              (list SEND_MESSAGE 0x3f
                (concat
                  PROTOCOL_PREFIX
                  (sha256tree
                    (c STATUTES_STRUCT
                      (c 'x' (* -1 byc_to_melt))
                    )
                  )
                )
                byc_coin_id_to_melt
              )
              (list REMARK)
            )
            (if (> (+ bid_xch_collateral_amount byc_to_melt) 0)
              ; get a melt confirmation from BYC tail
              ; assert that keeper absorbed their collateral + left over mojos from melting
              (list ASSERT_PUZZLE_ANNOUNCEMENT
                (sha256
                  OFFER_MOD_HASH
                  (sha256tree
                    (c
                      my_coin_id
                      (list
                        (list
                          target_puzzle_hash
                          (+ bid_xch_collateral_amount byc_to_melt)
                        )
                      )
                    )
                  )
                )
              )
              (list REMARK)
            )
            ; announce that we transferred collateral to target puzzle hash
            (if (> initiator_incentive 0)
              ; get an announcement from offer module that BYC was paid to initiator
              (list ASSERT_PUZZLE_ANNOUNCEMENT
                (sha256
                  (curry_hashes CAT_MOD_HASH
                    (sha256 ONE CAT_MOD_HASH)
                    (sha256 ONE byc_tail_hash)
                    OFFER_MOD_HASH
                  )
                  ; nonce = my_coin_id, payments = (initiator puzzle hash, initiator incentive fee)
                  (sha256tree
                    (c my_coin_id
                      (list
                        (list initiator_puzzle_hash initiator_incentive (list initiator_puzzle_hash))
                      )
                    )
                  )
                )
              )
              (list REMARK)
            )
          )
          (if (> byc_to_treasury 0)
            ; approve treasury coin to receive a deposit
            (c
              (assert-statute statutes_puzzle_hash STATUTE_TREASURY_MINIMUM_DELTA min_treasury_delta)
              (c
                (list SEND_MESSAGE 0x3f
                  (concat
                    PROTOCOL_PREFIX
                    (sha256tree (c STATUTES_STRUCT (c byc_to_treasury new_treasury_amount)))
                  )
                  treasury_coin_id
                )
                conditions
              )
            )
            ; maybe we set fees to zero for some reason, so don't charge anything
            conditions
          )
        )
      )
    )
  )
)