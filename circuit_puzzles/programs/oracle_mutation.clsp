(mod (ATOM_ANNOUNCER_MOD_HASH
      MOD_HASH
      STATUTES_STRUCT
      PRICES
      (@ oracle_mutation
        (
          statutes_inner_puzzle_hash
          m_of_n
          price_updatable_after_seconds
          price_updatable_threshold_bps
          price_delay
          current_timestamp
          price_announcers ; -> ((launcher_id args_hash price))
        )
      )
      input_announcement_asserts
      )

  (include *standard-cl-23.1*)
  (include curry.clib)
  (include condition_codes.clib)
  (include statutes_utils.clib)
  (include utils.clib)
  (include oracle.clib)

  (assign
    price_announcers_length (list-length price_announcers 0)
    statutes_puzzle_hash (calculate-statutes-puzzle-hash STATUTES_STRUCT statutes_inner_puzzle_hash)
    sorted_prices (mergesort price_announcers)
    ; get median price, divide length by 2 and add 1 to get the middle when length is even
    median_price (f (r (r (get-price sorted_prices (get-median-index price_announcers_length) 0))))
    announcer_conditions (generate-ann-conditions
      STATUTES_STRUCT
      ATOM_ANNOUNCER_MOD_HASH
      price_announcers
    )
    ; we add latest price to the end of the list
    (matured_price_info . cut_price_infos) (cut-price-infos PRICES (- current_timestamp price_delay) ())
    price_to_apply (c median_price current_timestamp)
    ; push new price to the end of the queue, we have a FILO queue
    new_price_infos (merge-lists
        cut_price_infos
        (list price_to_apply)
    )
    (next_price . next_price_updated) (if matured_price_info matured_price_info (c () ()))
    (last_price . last_price_updated) (last-item PRICES)
    ;###  main expression
    ; check that we haven't updated the price recently OR new price is much higher than previous
    (assert
      (> price_announcers_length (- m_of_n ONE))
      ; check that prices are unique
      (unique price_announcers)
      (> current_timestamp last_price_updated)
      (any
        (> (- current_timestamp last_price_updated) price_updatable_after_seconds)
        (if (> last_price 0)
          (> (abs (/ (* (- last_price median_price) PRECISION_BPS) last_price) ) price_updatable_threshold_bps)
          ; no prices in buffer, update now
          ONE
        )
        (not (r new_price_infos)) ; only latest price in the list, this is fine too as all other prices are expired
      )
      ; should be confirmed at least within 5 blocks since it was pushed to mempool
      (li
        (list ASSERT_BEFORE_SECONDS_ABSOLUTE (+ current_timestamp MAX_TX_BLOCK_TIME ))
        ; current_timestamp - 1 min should've passed already too, this is to ensure current timestamp is
        ; within a boundary of last_block > current_timestamp - 1 block < next block
        (list ASSERT_SECONDS_ABSOLUTE (- current_timestamp MAX_TX_BLOCK_TIME))
        (assert-statute statutes_puzzle_hash STATUTE_ORACLE_M_OF_N m_of_n)
        (assert-statute statutes_puzzle_hash STATUTE_ORACLE_PRICE_UPDATE_DELAY price_updatable_after_seconds)
        (assert-statute statutes_puzzle_hash STATUTE_ORACLE_PRICE_UPDATE_DELTA_BPS price_updatable_threshold_bps)
        (assert-statute statutes_puzzle_hash STATUTE_PRICE_DELAY price_delay)
        (if next_price
          (list CREATE_PUZZLE_ANNOUNCEMENT
            (sha256tree (c next_price (c next_price_updated (c current_timestamp price_delay))))
          )
          (list REMARK)
        )
        (list
          CREATE_COIN
          (curry_hashes MOD_HASH
            (sha256 ONE MOD_HASH)
            (sha256tree STATUTES_STRUCT)
            (sha256tree new_price_infos)
          )
          ; min singleton amount, force it to be ONE
          ONE
        )
        (list REMARK new_price_infos next_price)
        &rest (verify-announcement-asserts input_announcement_asserts announcer_conditions)
      )
    )
  )
)