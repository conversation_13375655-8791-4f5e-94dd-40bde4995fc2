(mod ((SINGLETON_MOD_HASH . SINGLETON_LAUNCHER_HASH)
       MOD_HASH STATUTES PRICE_INFO PAST_CUMULATIVE_STABILITY_FEE_DF PAST_CUMULATIVE_INTEREST_DF
       PRICE_UPDATE_COUNTER
       _ ; mutation_index
       (@ mutation_value (oracle_inner_puz_hash price last_updated current_timestamp))
       _ ; governance_curried_ars_hash
     )

  (include *standard-cl-23.1*)
  (include curry.clib)
  (include condition_codes.clib)
  (include statutes_utils.clib)
  (include utils.clib)

  (defun-inline price-delay-statute (statutes)
    (f (f (r (r (r (r (r (r (r statutes)))))))))
  )

  ; we're updating prices
  ; returning -> statutes, price_info, cumulative_stability_fee, cumulative_interest_rate, price_updates, mutation_conditions
  (list
    () ; set PREV_ANNOUNCE to nil
    STATUTES
    ; get the price, mutation value -> (oracle_inner_puz_hash statutes_struct price last_updated)
    (c price last_updated)
    ; to calculate current cumulative stability fee discount factor we need:
    ; - past cumulative stability fee discount factor
    ; - current stability fee discount factor
    ; - current timestamp
    ; - timestamp of previous calculation
    (calculate-cumulative-discount-factor
      PAST_CUMULATIVE_STABILITY_FEE_DF
      (f (f (r STATUTES)))
      last_updated ; current timestamp
      ; get previous timestamp when price was last updated
      ; NOTE: this assumes that at least one oracle is always present and active at position 0
      (r PRICE_INFO) ; prev timestamp
    )
    ; to calculate current cumulative stability fee discount factor we need:
    ; - past cumulative interest discount factor
    ; - current interest discount factor
    ; - current timestamp
    ; - timestamp of previous calculation
    (calculate-cumulative-discount-factor
      PAST_CUMULATIVE_INTEREST_DF
      ; current interest rate
      (f (f (r (r STATUTES))))
      last_updated
      ; get previous timestamp when price was last updated
      ; NOTE: this assumes that at least one oracle is always present and active at position 0
      (r PRICE_INFO)
    )
    ; increase the price updates counter to be used by announcers to claim rewards
    (+ PRICE_UPDATE_COUNTER 1)
    (assert
      ; new price should be fresher than current one
      (> last_updated (r PRICE_INFO))
      ; confirm oracle price info to update statutes price info
      (list
        ; no need to check current_timestamp as this is already done by oracle
        (list ASSERT_PUZZLE_ANNOUNCEMENT
          (sha256
            ; generate oracle singleton puzzle hash
            (curry_hashes
              SINGLETON_MOD_HASH
              (sha256tree
                (c SINGLETON_MOD_HASH
                  (c
                    (f (f STATUTES)) ; -> oracle_launcher_id
                    SINGLETON_LAUNCHER_HASH
                  )
                )
              )
              oracle_inner_puz_hash
            )
            (sha256tree (c price (c last_updated (c current_timestamp (price-delay-statute STATUTES)))))
          )
        )
      )
    )
  )
)