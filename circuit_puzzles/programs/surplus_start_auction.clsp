(mod
  (PAYOUT_MOD_HASH TREASURY_MOD_HASH
    (MOD_HASH CAT_MOD_HASH STATUTES_STRUCT BYC_TAIL_HASH CRT_TAIL_HASH LAUNCHER_ID BID_TTL MIN_BID_INCREASE_BPS BYC_LOT_AMOUNT LAST_BID)
    lineage_proof input_conditions
    (
      statutes_inner_puzzle_hash
      payout_coin_parent_id
      lot_amount
      my_coin_id
      bid_ttl
      min_bid_increase_bps
      treasury_coins ; -> ((parent_id launcher_id ring_prev_launcher_id current_amount withdraw_amount))
      treasury_maximum
    )
  )

  (include *standard-cl-23.1*)
  (include curry.clib)
  (include condition_codes.clib)
  (include statutes_utils.clib)
  (include utils.clib)


  (defun assert-treasury-coins-withdrawal (TREASURY_MOD_HASH CAT_MOD_HASH BYC_TAIL_HASH STATUTES_STRUCT treasury_maximum statutes_puzzle_hash
        treasury_coins ; any subset of treasury coins -> ((parent_id launcher_id ring_prev_launcher_id current_amount withdraw_amount))
        lot_amount ; amount to withdraw from treasury coins
        sum_amounts_withdrawn ; amount withdrawn from treasury coins. must be passed 0 when function is called
        new_treasury_coins_amount ; post-withdrawal amount of treasury coins. must be passed 0 when function is called
        input_conditions
      )
    (if treasury_coins
      (assign
        (parent_id launcher_id ring_prev_launcher_id current_amount withdraw_amount) (f treasury_coins)
        treasury_coin_id
          (calculate-coin-id
            parent_id
            (curry_hashes CAT_MOD_HASH
              (sha256 ONE CAT_MOD_HASH)
              (sha256 ONE BYC_TAIL_HASH)
              (curry_hashes
                TREASURY_MOD_HASH
                (sha256 ONE TREASURY_MOD_HASH)
                (sha256tree STATUTES_STRUCT)
                (sha256 ONE launcher_id)
                (sha256 ONE ring_prev_launcher_id)
              )
            )
            current_amount
          )
        (c
          ; send a message to approve treasury coins for withdrawal
          (list SEND_MESSAGE 0x3f
            (concat
              PROTOCOL_PREFIX
              (sha256tree (c STATUTES_STRUCT (c (* -1 withdraw_amount) (- current_amount withdraw_amount))))
            )
            treasury_coin_id
          )
          (assert-treasury-coins-withdrawal
            TREASURY_MOD_HASH CAT_MOD_HASH BYC_TAIL_HASH STATUTES_STRUCT treasury_maximum statutes_puzzle_hash
            (r treasury_coins)
            lot_amount
            (+ sum_amounts_withdrawn withdraw_amount)
            (- (+ new_treasury_coins_amount current_amount) withdraw_amount)
            input_conditions
          )
        )
      )
      ; we're withdrawing exactly what lot we require for this surplus auction
      ; and treasury needs to stay above maximum threshold, so to trigger surplus auction
      ; we need maximum + lot_amount
      (assert (= sum_amounts_withdrawn lot_amount) (> new_treasury_coins_amount treasury_maximum)
        (c
          (assert-statute statutes_puzzle_hash STATUTE_TREASURY_MAXIMUM treasury_maximum) ; end of recursion
          input_conditions
        )
      )
    )
  )

  (assign
    statutes_puzzle_hash (calculate-statutes-puzzle-hash STATUTES_STRUCT statutes_inner_puzzle_hash)
    payout_puzzle_hash (
      curry_hashes CAT_MOD_HASH
        (sha256 ONE CAT_MOD_HASH)
        (sha256 ONE BYC_TAIL_HASH)
        (curry_hashes PAYOUT_MOD_HASH
          (sha256 ONE PAYOUT_MOD_HASH)
          (sha256 ONE CRT_TAIL_HASH)
          (sha256 ONE MOD_HASH)
          (sha256 ONE my_coin_id)
        )
    )
    payout_coin_id (calculate-coin-id
        payout_coin_parent_id
        payout_puzzle_hash
        0 ; initial amount for the coin that holds our lot should be 0
    )
    (li
      (list ASSERT_MY_PUZZLE_HASH
        (curry_hashes CAT_MOD_HASH
          (sha256 ONE CAT_MOD_HASH)
          (sha256 ONE CRT_TAIL_HASH)
          (curry_hashes
            MOD_HASH
            (sha256 ONE MOD_HASH)
            (sha256tree STATUTES_STRUCT)
            (sha256 ONE ()); LAUNCHER_ID
            (sha256 ONE 0) ; BID_TTL
            (sha256 ONE 0) ; MIN_BID_INCREASE_BPS
            (sha256 ONE 0) ; BYC_LOT_AMOUNT
            (sha256 ONE ()) ; LAST_BID
          )
        )
      )
      ; recreate itself setting the launcher_id, empty bid and coin amount is 0
      (list
        CREATE_COIN
        (curry_hashes
          MOD_HASH
          (sha256 ONE MOD_HASH)
          (sha256tree STATUTES_STRUCT)
          (sha256 ONE my_coin_id) ; launcher ID
          (sha256 ONE bid_ttl)
          (sha256 ONE min_bid_increase_bps)
          (sha256 ONE lot_amount)
          (sha256 ONE ()) ; no bid yet
        )
        0
      )
      (list ASSERT_MY_AMOUNT 0) ; enforece eve amount
      (list ASSERT_MY_COIN_ID my_coin_id)
      (assert-statute statutes_puzzle_hash STATUTE_SURPLUS_AUCTION_BID_TTL bid_ttl)
      (assert-statute statutes_puzzle_hash STATUTE_MINIMUM_BID_INCREASE_BPS min_bid_increase_bps)
      (assert-statute statutes_puzzle_hash STATUTE_SURPLUS_AUCTION_LOT lot_amount)
      ; assert payout coin received the lot amount
      (list RECEIVE_MESSAGE 0x3f
        (concat PROTOCOL_PREFIX (sha256tree lot_amount))
        payout_coin_id
      )
      (list
        REMARK
        PROTOCOL_PREFIX
        my_coin_id
        bid_ttl
        min_bid_increase_bps
        lot_amount
        0 ; bid crt amount still 0
        () ; last bid
      ) 
      &rest
      (assert-treasury-coins-withdrawal
        TREASURY_MOD_HASH
        CAT_MOD_HASH
        BYC_TAIL_HASH
        STATUTES_STRUCT
        treasury_maximum
        statutes_puzzle_hash
        treasury_coins
        lot_amount ; amount of byc to withdraw
        0 ; sum amounts withdrawn
        0 ; post-withdrawal treasury coins amount
        input_conditions
      )
    )
  )
)