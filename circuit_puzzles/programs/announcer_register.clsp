; register the announcer with announcer registry coin
(mod ((MOD_HASH STATUTES_STRUCT LAUNCHER_ID INNER_PUZZLE_HASH APPROVED DEPOSIT DELAY VALUE MIN_DEPOSIT
        CLAIM_COUNTER COOLDOWN_START PENALIZABLE_AT TIMESTAMP_EXPIRES
      )
      ; solution
      inner_puzzle_hash new_puzzle_hash deposit input_conditions
      (registry_mod_hash registry_args_hash registry_claim_counter
        target_puzzle_hash statutes_inner_puzzle_hash min_deposit
      )
     )

  (include *standard-cl-23.1*)
  (include condition_codes.clib)
  (include curry.clib)
  (include utils.clib)
  (include statutes_utils.clib)
  (include announcer.clib)

  (assign
    announcer_registry_puzhash (tree_hash_of_apply registry_mod_hash registry_args_hash)
    statutes_puzzle_hash (calculate-statutes-puzzle-hash STATUTES_STRUCT statutes_inner_puzzle_hash)
    (assert
      inner_puzzle_hash ; must be owner
      APPROVED
      (> registry_claim_counter CLAIM_COUNTER)
      (> deposit (- MIN_DEPOSIT 1)) ; must not be penalizable for deposit
      (> MIN_DEPOSIT (- min_deposit 1)) ; must not be penalizable for min deposit
      (c
        (list SEND_MESSAGE 0x12
          (concat
            PROTOCOL_PREFIX
            (sha256tree
              (c STATUTES_STRUCT
                (c target_puzzle_hash
                  (c APPROVED
                     registry_claim_counter
                  )
                )
              )
            )
          )
          announcer_registry_puzhash
        )
        ; must not be expired
        (c
          (list ASSERT_BEFORE_SECONDS_ABSOLUTE TIMESTAMP_EXPIRES)
          (c
            (assert-statute statutes_puzzle_hash STATUTE_ANNOUNCER_MINIMUM_DEPOSIT min_deposit)
            (recreate-myself-condition MOD_HASH
              STATUTES_STRUCT
              LAUNCHER_ID
              INNER_PUZZLE_HASH
              APPROVED
              deposit
              DELAY
              VALUE
              MIN_DEPOSIT
              registry_claim_counter
              COOLDOWN_START
              PENALIZABLE_AT
              TIMESTAMP_EXPIRES
              input_conditions
            )
          )
        )
      )
    )
  )
)