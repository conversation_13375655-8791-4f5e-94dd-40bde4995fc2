(mod (
    (BILL MOD_HASH INNER_PUZZLE_HASH CAT_MOD_HASH CRT_TAIL_HASH STATUTES_STRUCT
     statutes_puzzle_hash)
    (
      amount
      new_bill_proposal_fee_mojos
      (@ new_bill (
        statute_index
        new_statute_value
        new_threshold_amount_to_propose
        new_veto_interval
        new_implementation_delay
        new_max_delta
      ))
      (@ current_statute (
        statute_value
        threshold_amount_to_propose
        veto_interval
        implementation_delay
        max_delta
      ))
      current_timestamp
      implementation_interval
    )
  )

  (include *standard-cl-23.1*)
  (include condition_codes.clib)
  (include statutes_utils.clib)
  (include utils.clib)

  (defconst ANN_PROPOSED '^')

  (assert
    (> amount threshold_amount_to_propose)
    (> new_threshold_amount_to_propose 0)
    (> new_veto_interval 0)
    (> new_implementation_delay 0)
    ; if we have a max_delta, make sure new value doesn't exceed it
    (if (= max_delta 0)
      ONE
      ; or new value must be within max_delta of current value
      (if (> new_statute_value statute_value)
        (> max_delta (- new_statute_value statute_value))
        (> max_delta (- statute_value new_statute_value))
      )
    )
    (list
      (c
        ; set proposal times
        (c (+ current_timestamp veto_interval) ; veto_interval_expiry
          (c (+ current_timestamp veto_interval implementation_delay) ; implementation_delay_expiry
            (+ current_timestamp veto_interval implementation_delay implementation_interval) ; implementation_interval_expiry
          )
        )
        new_bill
      )
      (list
        ; to avoid spamming the network with proposals, we require a fee to be paid
        (list RESERVE_FEE new_bill_proposal_fee_mojos)
        (list CREATE_COIN_ANNOUNCEMENT (concat PROTOCOL_PREFIX (sha256tree ANN_PROPOSED)))
        ; assert that passed statute index corresponds to correct statute
        ; and that passed statute values are correct
        (assert-full-statute
          statutes_puzzle_hash
          ; we use special statute for custom conditions limits
          (if (= statute_index MINUS_ONE) STATUTE_CUSTOM_CONDITIONS statute_index)
          current_statute
        )
        (list ASSERT_SECONDS_ABSOLUTE (- current_timestamp MAX_TX_BLOCK_TIME))
        (list ASSERT_BEFORE_SECONDS_ABSOLUTE (+ current_timestamp (* 5 MAX_TX_BLOCK_TIME)))
        (assert-statute statutes_puzzle_hash STATUTE_GOVERNANCE_BILL_PROPOSAL_FEE_MOJOS new_bill_proposal_fee_mojos)
        (assert-statute statutes_puzzle_hash STATUTE_GOVERNANCE_IMPLEMENTATION_INTERVAL implementation_interval)
      )
    )
  )
)