(mod
  (
    CAT_MOD_HASH BYC_TAIL_MOD_HASH RUN_TAIL_MOD_HASH TREASURY_MOD_HASH
    (@ VAULT_STATE
      (
       COLLATERAL PRINCIPAL AUCTION_STATE INNER_PUZZLE_HASH
       STATUTES_STRUCT DISCOUNTED_PRINCIPAL statutes_puzzle_hash
      )
    )
    (@ args
      (
         repay_amount ; amount of byc to being repaid
         sf_transfer_amount ; SFs to transfer to treasury
         statutes_cumulative_stability_fee_df
         byc_melting_coin_info
         minimum_debt_amount
         byc_treasury_coin_info
         min_treasury_delta
         price_info
         current_stability_fee_df
         current_timestamp
      )
    )
  )

  (include *standard-cl-23.1*)
  (include curry.clib)
  (include condition_codes.clib)
  (include statutes_utils.clib)
  (include utils.clib)
  (include vault.clib)

  (assign
    byc_tail_hash (curry_hashes BYC_TAIL_MOD_HASH (sha256tree STATUTES_STRUCT))
    cumulative_stability_fee_df (calculate-cumulative-discount-factor
      statutes_cumulative_stability_fee_df
      current_stability_fee_df
      ; don't allow anyone to pay less by borrowing from the future
      (+ current_timestamp (* 3 MAX_TX_BLOCK_TIME))
      (r price_info)
    )
    undiscounted_principal (undiscount-principal DISCOUNTED_PRINCIPAL cumulative_stability_fee_df)
    accrued_sf (calculate-total-fees undiscounted_principal PRINCIPAL 0)
    negative_principal_to_repay (- sf_transfer_amount repay_amount)
    new_principal (+ PRINCIPAL negative_principal_to_repay)
    new_discounted_principal (if (= repay_amount undiscounted_principal)
      0
      (- DISCOUNTED_PRINCIPAL
        (/ (* repay_amount PRECISION) cumulative_stability_fee_df)
      )
    )
    new_undiscounted_principal (undiscount-principal new_discounted_principal cumulative_stability_fee_df)
    (treasury_parent treasury_launcher_id treasury_prev_launcher_id treasury_amount) (if byc_treasury_coin_info byc_treasury_coin_info (list 0 0 0 0))
    treasury_coin_id (if byc_treasury_coin_info
      (calculate-byc-coin-id
        CAT_MOD_HASH
        byc_tail_hash
        (list
          treasury_parent
          treasury_amount
          (curry_hashes
            TREASURY_MOD_HASH
            (sha256 ONE TREASURY_MOD_HASH)
            (sha256tree STATUTES_STRUCT)
            (sha256 ONE treasury_launcher_id)
            (sha256 ONE treasury_prev_launcher_id)
          )
        )
      )
      ()
    )
    new_treasury_amount (+ sf_transfer_amount treasury_amount)
    byc_coin_id_to_melt (calculate-byc-coin-id
      CAT_MOD_HASH
      byc_tail_hash
      (list
        (f byc_melting_coin_info)
        (r byc_melting_coin_info)
        RUN_TAIL_MOD_HASH
      )
    )
    (assert
      (> repay_amount MINUS_ONE) ; repay amount must not be negative (but can be 0)
      (> undiscounted_principal (- repay_amount ONE)) ; cannot repay more than outstanding debt      
      (> sf_transfer_amount MINUS_ONE) ; cannot transfer a negative amount
      (> accrued_sf (- sf_transfer_amount ONE)) ; cannot transfer more than accrued SFs
      ; either we're above min debt or we're at zero debt, to avoid dusting attacks and keep vault liquidation costs at healthy levels
      (any (= new_undiscounted_principal 0) (> new_undiscounted_principal minimum_debt_amount))
      (any (= sf_transfer_amount 0) (> sf_transfer_amount min_treasury_delta)) ; if there is a deposit to treasury, it must exceed required min
      (any (> repay_amount 0) (> sf_transfer_amount 0)) ; noop if both are 0. not strictly necessary, unless we open repay up to keepers
      ; current time must be at least after last oracle update
      (> current_timestamp (r price_info))
      (list
        (list COLLATERAL new_principal AUCTION_STATE INNER_PUZZLE_HASH new_discounted_principal)
        (assign
          conditions (li
            (assert-statute statutes_puzzle_hash STATUTE_CUMULATIVE_STABILITY_FEE_DF statutes_cumulative_stability_fee_df)
            (assert-statute statutes_puzzle_hash STATUTE_VAULT_MINIMUM_DEBT minimum_debt_amount)
            (assert-price-info statutes_puzzle_hash price_info)
            (list ASSERT_SECONDS_ABSOLUTE (- current_timestamp MAX_TX_BLOCK_TIME))
            (list ASSERT_BEFORE_SECONDS_ABSOLUTE (+ current_timestamp MAX_TX_BLOCK_TIME))
            (assert-statute statutes_puzzle_hash STATUTE_STABILITY_FEE_DF current_stability_fee_df)
            &rest
            (if (> negative_principal_to_repay MINUS_ONE)
              (list )
              (list
                ; signal to tail that it can melt the BYC
                (list SEND_MESSAGE 0x3f
                  (concat
                    PROTOCOL_PREFIX
                    (sha256tree
                      (c STATUTES_STRUCT
                        (c 'x'
                          negative_principal_to_repay
                        )
                      )
                    )
                  )
                  byc_coin_id_to_melt
                )
              )
            )
          )
          (if (> sf_transfer_amount 0)
            ; send message to treasury coin ensure deposit was made
            (c
              (assert-statute statutes_puzzle_hash STATUTE_TREASURY_MINIMUM_DELTA min_treasury_delta)
              (c 
                (list SEND_MESSAGE 0x3f
                  (concat
                    PROTOCOL_PREFIX
                    (sha256tree (c STATUTES_STRUCT (c sf_transfer_amount new_treasury_amount)))
                  )
                  treasury_coin_id
                )
                conditions
              )
            )
            ; else no approvals needed
            conditions
          )
        )
      )
    )
  )
)