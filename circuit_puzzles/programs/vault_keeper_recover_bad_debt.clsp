(mod
  (
    CAT_MOD_HASH BYC_TAIL_MOD_HASH TREASURY_MOD_HASH RUN_TAIL_MOD_HASH
    (@ VAULT_STATE
      (
       COLLATERAL PRINCIPAL AUCTION_STATE INNER_PUZZLE_HASH
       STATUTES_STRUCT DISCOUNTED_PRINCIPAL statutes_puzzle_hash
      )
    )
    (@ args
      (
        reconcile_amount
        current_timestamp
        treasury_coin_info
        min_treasury_delta
      )
    )
  )

  (include *standard-cl-23.1*)
  (include utils.clib)
  (include vault.clib)
  (include statutes_utils.clib)
  (include condition_codes.clib)
  (include curry.clib)

  (assign
    byc_tail_hash (curry_hashes BYC_TAIL_MOD_HASH (sha256tree STATUTES_STRUCT))
    (treasury_parent treasury_launcher_id treasury_prev_launcher_id treasury_amount) (if treasury_coin_info treasury_coin_info (list 0 0 0 0))
    new_treasury_amount (- treasury_amount reconcile_amount)
    treasury_coin_id (calculate-byc-coin-id
      CAT_MOD_HASH
      byc_tail_hash
      (list
        treasury_parent
        treasury_amount
        (curry_hashes
          TREASURY_MOD_HASH
          (sha256 ONE TREASURY_MOD_HASH)
          (sha256tree STATUTES_STRUCT)
          (sha256 ONE treasury_launcher_id)
          (sha256 ONE treasury_prev_launcher_id)
        )
      )
    )
    (
      auction_start_time
      start_price
      step_price_decrease_factor
      step_time_interval
      initiator_puzzle_hash
      initiator_incentive_balance
      auction_ttl
      byc_to_treasury_balance
      byc_to_melt_balance
    ) (assert AUCTION_STATE AUCTION_STATE)
    leftover_byc_to_melt_balance (- byc_to_melt_balance reconcile_amount)
    auction_state (if (= leftover_byc_to_melt_balance 0)
      ; paid off all debt, reset auction state
      ()
      (list
        0 ; auction_start_time
        0 ; start_price
        0 ; step_price_decrease_factor
        0 ; step_time_interval
        0 ; initiator_puzzle_hash
        0 ; initiator_incentive_balance. initiator is no longer getting paid
        0 ; auction_ttl
        0 ; byc_to_treasury_balance. fees paid to treasury cancel out
        leftover_byc_to_melt_balance
        0
      )
    )
    (assert
      ; check that there is no collateral left
      (= COLLATERAL 0)
      ; must recover debt fully or partially, but amount must be at least min treasury delta
      (> leftover_byc_to_melt_balance -1)
      (> reconcile_amount 0)
      (any (> reconcile_amount min_treasury_delta) (= reconcile_amount byc_to_melt_balance))
      (list
        (list
          COLLATERAL
          0 ; principal
          auction_state
          INNER_PUZZLE_HASH
          0 ; discounted principal
        )
        (list
          (assert-statute statutes_puzzle_hash STATUTE_TREASURY_MINIMUM_DELTA min_treasury_delta)
          ; signal to tail that it can melt BYC with certain amount
          ; treasury coins creates two coins, first is a new treasury coin
          ; with new amount reduced by reconcile amount, second is a melting coin
          ; that uses RUN_TAIL puzzle to enforce running the tail of BYC
          (list SEND_MESSAGE 0x3f
            (concat
              PROTOCOL_PREFIX
              (sha256tree
                (c STATUTES_STRUCT
                  (c "x" (* -1 reconcile_amount))
                )
              )
            )
            (calculate-byc-coin-id
              CAT_MOD_HASH
              byc_tail_hash
              (list
                treasury_coin_id
                reconcile_amount
                RUN_TAIL_MOD_HASH
              )
            )
          )
          ; approve treasury coin to allow a withdrawal
          (list SEND_MESSAGE 0x3f
            (concat
              PROTOCOL_PREFIX
              (sha256tree (c STATUTES_STRUCT (c (* -1 reconcile_amount) (concat new_treasury_amount RUN_TAIL_MOD_HASH))))
            )
            treasury_coin_id
          )
          (list ASSERT_SECONDS_ABSOLUTE (- current_timestamp MAX_TX_BLOCK_TIME))
          (list ASSERT_BEFORE_SECONDS_ABSOLUTE (+ current_timestamp MAX_TX_BLOCK_TIME))
        )
      )
    )
  )
)
