(mod ((MOD_HASH STATUTES_STRUCT LAUNCHER_ID INNER_PUZZLE_HASH APPROVED DEPOSIT DELAY VALUE MIN_DEPOSIT
        CLAIM_COUNTER COOLDOWN_START PENALIZABLE_AT TIMESTAMP_EXPIRES
      )
      ; solution
      inner_puzzle_hash new_puzzle_hash deposit input_conditions
      _
     )

  ; not using cl-23 standard to save costs
  (include condition_codes.clib)
  (include curry.clib)
  (include prefixes.clib)

  (defconst ONE 1)

  (defun sha256tree (TREE)
    (if (l TREE)
      (sha256 2 (sha256tree (f TREE)) (sha256tree (r TREE)))
      (sha256 ONE TREE)
    )
  )

  (c
    (list CREATE_PUZZLE_ANNOUNCEMENT
      (concat
        PROTOCOL_PREFIX
        (sha256tree
          (c STATUTES_STRUCT
            (c LAUNCHER_ID (c APPROVED VALUE))
          )
        )
      )
    )
    (c
      (list ASSERT_BEFORE_SECONDS_ABSOLUTE TIMESTAMP_EXPIRES)
      (c
        (list CREATE_COIN
          (curry_hashes MOD_HASH
            (sha256 ONE MOD_HASH)
            (sha256tree STATUTES_STRUCT)
            (sha256 ONE LAUNCHER_ID)
            (sha256 ONE INNER_PUZZLE_HASH)
            (sha256 ONE APPROVED)
            (sha256 ONE DEPOSIT)
            (sha256 ONE DELAY)
            (sha256tree VALUE)
            (sha256 ONE MIN_DEPOSIT)
            (sha256 ONE CLAIM_COUNTER)
            (sha256 ONE COOLDOWN_START)
            (sha256 ONE PENALIZABLE_AT)
            (sha256 ONE TIMESTAMP_EXPIRES)
          )
          DEPOSIT
          (list INNER_PUZZLE_HASH)
        )
        ; driver remark to reveal the puzzle hash
        (c
          (list REMARK
            PROTOCOL_PREFIX
            LAUNCHER_ID
            INNER_PUZZLE_HASH
            DEPOSIT
            APPROVED
            DELAY
            VALUE
            MIN_DEPOSIT
            CLAIM_COUNTER
            COOLDOWN_START
            PENALIZABLE_AT
            TIMESTAMP_EXPIRES
          )
          input_conditions
        )
      )
    )
  )
)