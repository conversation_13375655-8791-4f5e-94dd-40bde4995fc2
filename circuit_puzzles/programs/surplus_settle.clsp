; This program is used to settle the winning surplus auction bid.
; Settlement is possible once the auction has ended, i.e. when the last bid has not been outbid after BID_TTL seconds.
; Running this operation burns the CRT locked into this coin (surplus auction)
; and unlocks the BYC in payout coin, which gets sent to the target puzzle hash of the winning bid.
(mod
  (PAYOUT_MOD_HASH RUN_TAIL_MOD_HASH
    (MOD_HASH CAT_MOD_HASH STATUTES_STRUCT BYC_TAIL_HASH CRT_TAIL_HASH LAUNCHER_ID BID_TTL MIN_BID_INCREASE_BPS BYC_LOT_AMOUNT LAST_BID)
    lineage_proof input_conditions
    (
      statutes_inner_puzzle_hash
      current_timestamp
      payout_coin_parent_id
      my_amount
      my_coin_id
    )
  )

  (include *standard-cl-23.1*)
  (include curry.clib)
  (include condition_codes.clib)
  (include statutes_utils.clib)
  (include utils.clib)

  (assign
    statutes_puzzle_hash (calculate-statutes-puzzle-hash STATUTES_STRUCT statutes_inner_puzzle_hash)
    (target_puzzle_hash . timestamp) LAST_BID
    payout_puzzle_hash (
      curry_hashes CAT_MOD_HASH
        (sha256 ONE CAT_MOD_HASH)
        (sha256 ONE BYC_TAIL_HASH)
        (curry_hashes PAYOUT_MOD_HASH
          (sha256 ONE PAYOUT_MOD_HASH)
          (sha256 ONE CRT_TAIL_HASH)
          (sha256 ONE MOD_HASH)
          (sha256 ONE LAUNCHER_ID)
        )
    )
    payout_coin_id (calculate-coin-id
        payout_coin_parent_id
        payout_puzzle_hash
        BYC_LOT_AMOUNT
    )
    melt_coin_id (calculate-coin-id
      my_coin_id
      (curry_hashes CAT_MOD_HASH
        (sha256 ONE CAT_MOD_HASH)
        (sha256 ONE CRT_TAIL_HASH)
        RUN_TAIL_MOD_HASH
      )
      my_amount
    )
    (assert
      ; must have a bid
      LAST_BID
      ; ttl for last bid should've expired
      (> (- current_timestamp timestamp) BID_TTL)
      (li
        (list ASSERT_MY_AMOUNT my_amount)
        (list ASSERT_MY_COIN_ID my_coin_id)
        (list ASSERT_MY_PUZZLE_HASH
          (curry_hashes CAT_MOD_HASH
            (sha256 ONE CAT_MOD_HASH)
            (sha256 ONE CRT_TAIL_HASH)
            (curry_hashes
              MOD_HASH
              (sha256 ONE MOD_HASH)
              (sha256tree STATUTES_STRUCT)
              (sha256 ONE LAUNCHER_ID)
              (sha256 ONE BID_TTL)
              (sha256 ONE MIN_BID_INCREASE_BPS)
              (sha256 ONE BYC_LOT_AMOUNT)
              (sha256tree LAST_BID)
            )
          )
        )
        (list CREATE_COIN RUN_TAIL_MOD_HASH my_amount)
        ; current_time minus one tx block time should already be in the past
        (list ASSERT_SECONDS_ABSOLUTE (- current_timestamp MAX_TX_BLOCK_TIME))
        ; make sure that current_timestamp hasn't happened yet, allow it to be in mempool for 5 tx blocks
        (list ASSERT_BEFORE_SECONDS_ABSOLUTE (+ current_timestamp (* 5 MAX_TX_BLOCK_TIME)))
        (list SEND_MESSAGE 0x3f
          (concat
            PROTOCOL_PREFIX
            (sha256tree
              (c
                STATUTES_STRUCT
                (c
                  ()
                  ; negative amount to signal melt
                  (* -1 my_amount)
                )
              )
            )
          )
          melt_coin_id
        )
        ; assert payout coin transferred the lot amount to the winner (target_puzzle_hash)
        (list SEND_MESSAGE 0x3f
          (concat
            PROTOCOL_PREFIX
            (sha256tree (c BYC_LOT_AMOUNT (c target_puzzle_hash LAUNCHER_ID)))
          )
          payout_coin_id
        )
        (list ASSERT_MY_PARENT_ID
          (calculate-coin-id
            (f lineage_proof)
            (curry_hashes
              CAT_MOD_HASH
              (sha256 ONE CAT_MOD_HASH)
              (sha256 ONE CRT_TAIL_HASH)
              (tree_hash_of_apply MOD_HASH (f (r (r lineage_proof))))
            )
            ; parent bid amount
            (f (r lineage_proof))
          )
        )
        &rest
        input_conditions
      )
    )
  )
)