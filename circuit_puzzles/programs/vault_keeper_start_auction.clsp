(mod
  (
    (@ VAULT_STATE
      (
       COLLATERAL PRINCIPAL AUCTION_STATE INNER_PUZZLE_HASH
       STATUTES_STRUCT DISCOUNTED_PRINCIPAL statutes_puzzle_hash
      )
    )
    (@ args
      (
        start_only ; -> () or (initiator_incentive_flat_fee initiator_incentive_relative_fee_bps liquidation_ratio statutes_cumulative_stability_fee_df current_stability_fee_df liquidation_penalty_bps)
        current_time
        step_time_interval
        step_price_decrease_factor
        price_info
        starting_price_factor
        initiator_puzzle_hash
        auction_ttl
        minimum_bid_amount
      )
    )
  )

  (include *standard-cl-23.1*)
  (include vault.clib)
  (include statutes_utils.clib)
  (include condition_codes.clib)
  (include utils.clib)

  ; NOTE: as in tradfi loan agreements, if the lender seizes collateral, there’s no further interest being
  ; charged on the borrower anymore (whereas it typically would if there’s a late payment only
  ; that does not allow the lender to seize collateral)
  (assign
    (
      initiator_incentive_flat_fee
      initiator_incentive_relative_fee_bps
      liquidation_ratio
      statutes_cumulative_stability_fee_df
      current_stability_fee_df
      liquidation_penalty_bps
    ) (if start_only (assert (not AUCTION_STATE) start_only) (assert AUCTION_STATE (list 0 0 1 1 1 0)))
    ; start only
    cumulative_stability_fee_df (calculate-cumulative-discount-factor
      statutes_cumulative_stability_fee_df
      current_stability_fee_df
      ; don't allow anyone to exploit our processing interval buffer to get tx into mempool and borrow from the future
      (+ current_time (* 3 MAX_TX_BLOCK_TIME))
      (r price_info)
    )
    undiscounted_principal (undiscount-principal DISCOUNTED_PRINCIPAL cumulative_stability_fee_df)
    min_collateral_amount (get-min-collateral-amount
      undiscounted_principal
      liquidation_ratio
      (f price_info)
    )
    initiator_incentive_relative (/ (* undiscounted_principal initiator_incentive_relative_fee_bps) PRECISION_BPS)
    initiator_incentive_balance (+ initiator_incentive_flat_fee initiator_incentive_relative)
    total_fees (calculate-total-fees
      undiscounted_principal
      PRINCIPAL
      liquidation_penalty_bps
    )
    ; start and restart
    start_price (/ (* (f price_info) starting_price_factor) PRECISION_BPS) ; start_price -> last_price * starting_price_factor
    auction_state (list
      current_time
      start_price
      step_price_decrease_factor
      step_time_interval
      initiator_puzzle_hash
      (if AUCTION_STATE
        (f (r (r (r (r (r AUCTION_STATE)))))) ; initiator_incentive_balance
        initiator_incentive_balance
      )
      auction_ttl
      (if AUCTION_STATE
        (f (r (r (r (r (r (r (r AUCTION_STATE)))))))) ; byc_to_treasury_balance
        (- total_fees initiator_incentive_balance)
      )
      (if AUCTION_STATE
        (f (r (r (r (r (r (r (r (r AUCTION_STATE))))))))) ; byc_to_melt_balance
        PRINCIPAL
      )
      minimum_bid_amount
    )
    conditions (list
      (list ASSERT_SECONDS_ABSOLUTE (- current_time MAX_TX_BLOCK_TIME))
      (list ASSERT_BEFORE_SECONDS_ABSOLUTE (+ current_time MAX_TX_BLOCK_TIME))
      (assert-price-info statutes_puzzle_hash price_info)
      (assert-statute statutes_puzzle_hash STATUTE_VAULT_AUCTION_STARTING_PRICE_FACTOR starting_price_factor)
      (assert-statute statutes_puzzle_hash STATUTE_VAULT_AUCTION_PRICE_DECREASE_BPS step_price_decrease_factor)
      (assert-statute statutes_puzzle_hash STATUTE_VAULT_AUCTION_PRICE_TTL step_time_interval)
      (assert-statute statutes_puzzle_hash STATUTE_VAULT_AUCTION_TTL auction_ttl)
      (assert-statute statutes_puzzle_hash STATUTE_VAULT_AUCTION_MINIMUM_BID minimum_bid_amount)
    )
    (list
      (list
        COLLATERAL
        0 ; PRINCIPAL
        auction_state
        INNER_PUZZLE_HASH
        0 ; DISCOUNTED_PRINCIPAL
      )
      (if (not AUCTION_STATE)
        ; start auction
        (assert
          ; we must be below min collateral ratio to start an auction
          (> min_collateral_amount COLLATERAL)
          (li
            (assert-statute statutes_puzzle_hash STATUTE_VAULT_LIQUIDATION_RATIO_PCT liquidation_ratio)
            (assert-statute statutes_puzzle_hash STATUTE_CUMULATIVE_STABILITY_FEE_DF statutes_cumulative_stability_fee_df)
            (assert-statute statutes_puzzle_hash STATUTE_STABILITY_FEE_DF current_stability_fee_df)
            (assert-statute statutes_puzzle_hash STATUTE_VAULT_INITIATOR_INCENTIVE_BPS initiator_incentive_relative_fee_bps)
            (assert-statute statutes_puzzle_hash STATUTE_VAULT_INITIATOR_INCENTIVE_FLAT initiator_incentive_flat_fee)
            (assert-statute statutes_puzzle_hash STATUTE_VAULT_LIQUIDATION_PENALTY_BPS liquidation_penalty_bps)
            &rest
            conditions
          )
        )
        ; restart auction
        (assert
          ; auction must have timed out
          (> (- current_time (f AUCTION_STATE)) auction_ttl)
          ; there should be collateral to auction off, o/w it's bad debt
          (> COLLATERAL 0)
          conditions
        )
      )
    )
  )
)