(mod
  (
    (BILL MOD_HASH INNER_PUZZLE_HASH CAT_MOD_HASH CRT_TAIL_HASH STATUTES_STRUCT
     statutes_puzzle_hash
    )
    (
      _ ; amount
      target_bill_hash
      target_coin_id
    )
  )

  (include *standard-cl-23.1*)
  (include condition_codes.clib)
  (include prefixes.clib)


  (list
    BILL
    (list
      (list SEND_MESSAGE 0x3f (concat PROTOCOL_PREFIX target_bill_hash) target_coin_id)
    )
  )

)
