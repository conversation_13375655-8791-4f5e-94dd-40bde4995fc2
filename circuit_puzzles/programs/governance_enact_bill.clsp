(mod
  (
    (BILL MOD_HASH INNER_PUZZLE_HASH CAT_MOD_HASH CRT_TAIL_HASH STATUTES_STRUCT
     statutes_puzzle_hash
    )
    (
      _ ; amount
      current_timestamp
    )
  )

  (include *standard-cl-23.1*)
  (include condition_codes.clib)
  (include prefixes.clib)
  (include utils.clib)

  (defconst ANN_ENACTED '$')

  (assign
    (
      (veto_period_expiry implementation_delay_expiry . implementation_interval_expiry)
      new_statute_index
      new_statute_value
      new_threshold_amount_to_propose
      new_veto_seconds
      new_delay_seconds
      new_max_delta
    ) BILL
    (assert
      ; can only implement in implementation interval
      (> current_timestamp implementation_delay_expiry)
      (> implementation_interval_expiry current_timestamp)
      (list
        ; if enactment passes, reset this coin's bill state
        ()
        ; conditions to return
        (list
          (list SEND_MESSAGE 0x12
            ; we skip the first two elements that are used for voting process, they're not stored in statutes
            (concat PROTOCOL_PREFIX (sha256tree (r BILL)))
            statutes_puzzle_hash
          )
          (list CREATE_COIN_ANNOUNCEMENT (concat PROTOCOL_PREFIX (sha256tree ANN_ENACTED)))
          (list ASSERT_SECONDS_ABSOLUTE (- current_timestamp MAX_TX_BLOCK_TIME))
          (list ASSERT_BEFORE_SECONDS_ABSOLUTE (+ current_timestamp (* 5 MAX_TX_BLOCK_TIME)))
        )
      )
    )
  )
)