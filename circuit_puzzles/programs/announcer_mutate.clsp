; owner operation
; updates atom value of the announcer. can optionally topup or reduce deposit
(mod ((MOD_HASH STATUTES_STRUCT LAUNCHER_ID INNER_PUZZLE_HASH APPROVED DEPOSIT DELAY VALUE MIN_DEPOSIT
        CLAIM_COUNTER COOLDOWN_START PENALIZABLE_AT TIMESTAMP_EXPIRES
      )
      ; solution
      inner_puzzle_hash new_puzzle_hash deposit input_conditions
      (current_timestamp new_value)
     )

  ; not using cl-23 standard to save costs
  (include condition_codes.clib)
  (include curry.clib)
  (include prefixes.clib)

  (defconst MAX_TX_BLOCK_TIME 120)
  (defconst ONE 1)

  (defun sha256tree (TREE)
    (if (l TREE)
      (sha256 2 (sha256tree (f TREE)) (sha256tree (r TREE)))
      (sha256 ONE TREE)
    )
  )

  (if (all
        LAUNCHER_ID
        inner_puzzle_hash ; must be owner
        (> deposit (- MIN_DEPOSIT 1)) ; deposit must be no less than min deposit to pay for fees and penalties
      )
    (c
      (list ASSERT_SECONDS_ABSOLUTE (- current_timestamp MAX_TX_BLOCK_TIME))
      (c
        (list ASSERT_BEFORE_SECONDS_ABSOLUTE (+ current_timestamp MAX_TX_BLOCK_TIME))
        (c
          (list CREATE_COIN
            (curry_hashes MOD_HASH
              (sha256 ONE MOD_HASH)
              (sha256tree STATUTES_STRUCT)
              (sha256 ONE LAUNCHER_ID)
              (sha256 ONE (if new_puzzle_hash new_puzzle_hash INNER_PUZZLE_HASH))
              (sha256 ONE APPROVED)
              (sha256 ONE deposit)
              (sha256 ONE DELAY)
              (sha256tree new_value)
              (sha256 ONE MIN_DEPOSIT)
              (sha256 ONE CLAIM_COUNTER)
              (sha256 ONE COOLDOWN_START)
              (sha256 ONE PENALIZABLE_AT)
              (sha256 ONE (+ current_timestamp DELAY))
            )
            deposit
            (list (if new_puzzle_hash new_puzzle_hash INNER_PUZZLE_HASH))
          )
          (c
            (list REMARK
              PROTOCOL_PREFIX
              LAUNCHER_ID
              (if new_puzzle_hash new_puzzle_hash INNER_PUZZLE_HASH)
              deposit
              APPROVED
              DELAY
              new_value
              MIN_DEPOSIT
              CLAIM_COUNTER
              COOLDOWN_START
              PENALIZABLE_AT
              (+ current_timestamp DELAY)
            )
            input_conditions
          )
        )
      )
    )
    (x)
  )
)