(mod (RUN_TAIL_MOD_HASH ; fixed
      STATUTES_STRUCT
      Truths
      parent_is_cat
      lineage_proof
      delta
      inner_conditions
      (@ solution
        (
          vault_parent_id
          vault_mod_hash
          vault_curried_args_hash
          vault_amount
          statutes_inner_puzzle_hash
          approval_mod_hashes
          current_coin_amount
        )
      )
     )

  (include *standard-cl-23.1*)
  (include condition_codes.clib)
  (include cat_truths.clib)
  (include curry.clib)
  (include statutes_utils.clib)
  (include utils.clib)
  (include condition_filtering.clib)

  (if (any (= delta 0) (> 0 delta))
    ; we're issuing or melting
    (assign
      vault_coin_id (calculate-coin-id
        vault_parent_id
        (tree_hash_of_apply vault_mod_hash vault_curried_args_hash)
        vault_amount
      )
      CAT_MOD_HASH (cat_mod_hash_truth Truths)
      CAT_MOD_HASH_HASH (cat_mod_hash_hash_truth Truths)
      byc_tail_hash (cat_tail_program_hash_truth Truths)
      statutes_puzzle_hash (calculate-statutes-puzzle-hash STATUTES_STRUCT statutes_inner_puzzle_hash)
      (assert
        ; check for cheeky conditions
        (fail-on-protocol-condition inner_conditions)
        ; we only support collateral vault as approval mod (they are the only ones that can issue BYC)
        (= vault_mod_hash (f approval_mod_hashes))
        (any
          ; when melting, parent must be a cat, and delta must be negative (indicates melt)
          ; we're melting the whole coin amount so should match
          (all parent_is_cat (> 0 delta) (= 0 (+ delta current_coin_amount)))
          ; when issuing, parent must not be a cat, and delta must be 0
          ; this means we need to rely on the coin amount to limit the issuance
          (all (not parent_is_cat) (= 0 delta))
        )
        (list
          ; we use current_coin_amount when issuing to limit the issuance to whatever vault approved
          (list ASSERT_MY_AMOUNT current_coin_amount)
          ; this tail can only be called in run tail puzzle to avoid misuse
          (list ASSERT_MY_PUZZLE_HASH
            (curry_hashes CAT_MOD_HASH
              CAT_MOD_HASH_HASH
              (sha256tree byc_tail_hash)
              RUN_TAIL_MOD_HASH
            )
          )
          (list RECEIVE_MESSAGE 0x3f
            (concat
              PROTOCOL_PREFIX
              (sha256tree
                (c
                  STATUTES_STRUCT ; need to tie it to statutes struct
                  (c
                    (if (> 0 delta) "x" "i")
                    (if (> 0 delta) delta current_coin_amount)
                  )
                )
              )
            )
            vault_coin_id
          )
          (assert-statute statutes_puzzle_hash STATUTE_APPROVAL_MOD_HASHES_HASH (sha256tree approval_mod_hashes))
        )
      )
    )
    (x) ; no minting
  )
)
